<template>
  <BaseDrawer v-model:show="visible" title="添加跟进记录" :width="600" :on-confirm="handleSubmit">
    <n-form
      ref="formRef"
      :model="formModel"
      :rules="rules"
      label-placement="left"
      label-width="120"
    >
      <!-- 跟进方式 -->
      <n-form-item path="followUpType" label="跟进方式:">
        <n-radio-group v-model:value="formModel.followUpType">
          <n-space>
            <n-radio
              v-for="option in followUpMethodOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 通讯状态 -->
      <n-form-item path="communicationStatus" label="通讯状态:">
        <n-radio-group v-model:value="formModel.communicationStatus">
          <n-space>
            <n-radio
              v-for="option in currentCommunicationStatusOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
              :on-update:value="(formModel.communicationDetail = null)"
              :disabled="
                formModel.followUpType === followUpMethodEnum.PHONE &&
                communicationStatus === communicationStatusEnum.CONNECTED &&
                option.value !== communicationStatusEnum.CONNECTED
              "
            />
          </n-space>
        </n-radio-group>
      </n-form-item>
      <!-- 通讯详情 -->
      <n-form-item
        v-if="phoneCommunicationDetailOptions.length > 0"
        path="communicationDetail"
        label="通讯详情:"
      >
        <n-radio-group v-model:value="formModel.communicationDetail">
          <n-space>
            <n-radio
              v-for="option in phoneCommunicationDetailOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>
      <!-- 意向度 -->
      <n-form-item path="intent" label="意向度:">
        <RadioButtonPicker v-model="formModel.intent" :options="intentionOptions" />
      </n-form-item>

      <!-- 是否加微 -->
      <n-form-item path="addWeChat" label="是否加微:">
        <n-radio-group v-model:value="formModel.addWeChat">
          <n-space>
            <n-radio
              v-for="option in yesNoOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 跟进备注 -->
      <n-form-item path="remark" label="跟进备注:">
        <n-input
          v-model:value="formModel.remark"
          type="textarea"
          :autosize="{ minRows: 3 }"
          maxlength="200"
          show-count
        />
      </n-form-item>

      <!-- 是否创建待办 -->
      <n-form-item path="addAgent" label="是否创建待办:">
        <n-radio-group v-model:value="formModel.addAgent">
          <n-space>
            <n-radio
              v-for="option in yesNoOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 待办事项 -->
      <div v-if="formModel.addAgent === 1" class="todo-section">
        <n-form-item path="type" label="待办类型:">
          <n-select v-model:value="formModel.type" :options="todoTypeOptions" />
        </n-form-item>
        <n-form-item path="agentTime" label="待办处理时间:">
          <n-date-picker v-model:value="formModel.agentTime" type="datetime" style="width: 100%" />
        </n-form-item>
        <n-form-item path="agentRemark" label="待办说明:">
          <n-input
            v-model:value="formModel.agentRemark"
            type="textarea"
            :autosize="{ minRows: 3 }"
            maxlength="200"
            show-count
          />
        </n-form-item>
      </div>
    </n-form>
  </BaseDrawer>
</template>

<script lang="ts" setup>
  import emitter from '@/utils/eventBus';
  import { ref, reactive, watch, computed, watchEffect, type PropType } from 'vue';
  import {
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NDatePicker,
    NRadioGroup,
    NRadio,
    NSpace,
    type FormInst,
    type FormRules,
  } from 'naive-ui';
  import { BaseDrawer } from '@/components/Drawer';
  import { RadioButtonPicker } from '@/components/Form';
  import { addFollowUpRecord, type AddFollowUpRecordPayload } from '@/api/detail';
  import {
    intentionOptions,
    followUpMethodOptions,
    yesNoOptions,
    todoTypeOptions,
    communicationStatusEnum,
    followUpMethodEnum,
    wechatCommunicationStatusOptions,
    phoneCommunicationStatusOptions,
    phoneConnectedOptions,
    phoneNoConnectedOptions,
    communicationStatusEnumV2,
  } from '@/enums/detailEnum';

  const props = defineProps({
    communicationStatus: {
      type: Number as PropType<number | null>,
      default: null,
    },
  });

  const visible = ref(false);
  const clueId = ref(0);
  const emit = defineEmits(['submit-success']);

  const formRef = ref<FormInst | null>(null);

  const initialFormState = {
    followUpType: 1,
    communicationStatus: null,
    communicationDetail: null,
    intent: null,
    addWeChat: 0,
    remark: '',
    addAgent: 0,
    type: 1,
    agentTime: null,
    agentRemark: '',
  };

  const formModel = reactive<Omit<AddFollowUpRecordPayload, 'clueId'>>({ ...initialFormState });

  const rules = computed<FormRules>(() => {
    const activeRules: FormRules = {
      followUpType: {
        required: true,
        type: 'number',
        message: '请选择跟进方式',
        trigger: 'change',
      },
      communicationStatus: {
        required: true,
        type: 'number',
        message: '请选择通讯状态',
        trigger: 'change',
      },
      intent: { required: true, type: 'number', message: '请选择意向度', trigger: 'change' },
    };

    if (formModel.addAgent === 1) {
      activeRules.type = {
        required: true,
        type: 'number',
        message: '请选择待办类型',
        trigger: 'change',
      };
      activeRules.agentTime = {
        required: true,
        type: 'number',
        message: '请选择待办处理时间',
        trigger: 'change',
      };
    }
    return activeRules;
  });

  const currentCommunicationStatusOptions = computed(() => {
    if (formModel.followUpType === followUpMethodEnum.PHONE) {
      return phoneCommunicationStatusOptions;
    }
    if (formModel.followUpType === followUpMethodEnum.WECHAT) {
      return wechatCommunicationStatusOptions;
    }
    return [];
  });
  const phoneCommunicationDetailOptions = computed(() => {
    if (formModel.followUpType === followUpMethodEnum.PHONE) {
      if (formModel.communicationStatus === communicationStatusEnumV2.CONNECTED)
        return phoneConnectedOptions;
      if (formModel.communicationStatus === communicationStatusEnumV2.NOT_CONNECTED)
        return phoneNoConnectedOptions;
    }
    return [];
  });
  watch(
    () => formModel.followUpType,
    () => {
      formModel.communicationStatus = null;
    }
  );

  watch(
    () => formModel.addAgent,
    (isCreatingTodo) => {
      if (!isCreatingTodo) {
        formRef.value?.restoreValidation();
      }
    }
  );

  watchEffect(() => {
    if (
      formModel.followUpType === followUpMethodEnum.PHONE &&
      props.communicationStatus === communicationStatusEnum.CONNECTED
    ) {
      formModel.communicationStatus = communicationStatusEnum.CONNECTED;
    }
  });
  watch(
    () => visible.value,
    (show) => {
      if (!show) {
        // 重置表单数据
        Object.assign(formModel, { ...initialFormState });
      }
    }
  );
  async function handleSubmit() {
    try {
      await formRef.value?.validate();
      const payload: AddFollowUpRecordPayload = {
        clueId: clueId.value,
        ...formModel,
        ...(formModel.followUpType === followUpMethodEnum.WECHAT
          ? { communicationDetail: null }
          : {}),
      };

      await addFollowUpRecord(payload);

      window.$message.success('添加成功');
      emit('submit-success');
      emitter.emit('add-follow-up-success');
      // 重置表单数据
      Object.assign(formModel, { ...initialFormState });
    } catch (error) {
      window.$message.error('添加失败');
      return false;
    }
  }

  function openDrawer(id: number) {
    clueId.value = id;
    visible.value = true;
  }

  defineExpose({
    openDrawer,
  });
</script>

<style scoped>
  .todo-section {
    border: 1px dashed #ccc;
    padding: 16px;
    border-radius: 4px;
    margin-top: -10px;
  }
</style>
