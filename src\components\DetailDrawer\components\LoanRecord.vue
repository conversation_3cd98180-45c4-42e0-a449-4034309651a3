<template>
  <div class="table-actions mb-10px">
    <n-button type="primary" @click="openAddModal">新增签约</n-button>
  </div>

  <n-data-table
    :columns="columns"
    :data="data"
    :pagination="{ pageSize: 15 }"
    :scroll-x="2000"
    max-height="560px"
    :render-cell="renderCell"
  />
  <BaseModal
    v-model:show="showModal"
    title="更新订单状态"
    :width="600"
    positive-text="保存"
    :on-confirm="handleConfirm"
  >
    <n-form
      ref="formRef"
      :model="model"
      :rules="rules"
      label-placement="left"
      :label-width="100"
      validate-trigger="blur"
    >
      <n-form-item path="orderNode" label="订单节点">
        <n-select v-model:value="model.orderNode" :options="orderNodeOptions" />
      </n-form-item>
      <n-form-item path="nodeStatus" label="订单状态">
        <n-select v-model:value="model.nodeStatus" :options="orderStatusOptions" />
      </n-form-item>
    </n-form>
  </BaseModal>
  <BaseModal
    v-model:show="showSubmitModal"
    title="提交贷款进件"
    :width="600"
    positive-text="确认"
    :on-confirm="handleFallUpConfirm"
  >
    <n-form
      ref="fallUpFormRef"
      :model="fallUpModel"
      :rules="fallUpRules"
      label-placement="left"
      :label-width="100"
      validate-trigger="blur"
    >
      <n-form-item path="followCapital" label="进件资方">
        <n-select v-model:value="fallUpModel.followCapital" :options="capitalOptions" />
      </n-form-item>
      <p class="text-gray-400 text-12px">(置灰资方：由于已存在该用户不可再次进件)</p>
    </n-form>
  </BaseModal>
</template>

<script lang="ts" setup>
  import { h, ref, reactive, withDirectives, resolveDirective } from 'vue';
  import {
    NDataTable,
    NButton,
    NForm,
    NFormItem,
    NSelect,
    NTime,
    type FormInst,
    type FormRules,
  } from 'naive-ui';
  import { BaseModal } from '@/components/Modal';
  import type { DataTableColumns } from 'naive-ui';
  import type { LoanOrderRecord, fallUpParams } from '@/api/detail';
  import { orderNodeOptions, orderStatusOptions } from '@/enums/detailEnum';
  import { updateOrderStatus } from '@/api/detail';
  import type { UpdateOrderStatusParams } from '@/api/detail';

  withDefaults(
    defineProps<{
      data1: LoanOrderRecord[];
    }>(),
    {}
  );
  const data = [
    {
      innerOrderNo: '', // 订单ID
      loanOrderNoTODO: 'string', //贷款订单id
      managementCode: 'string', // 进件资方
      productName: 'string', // 进件产品
      productRate: ' string', // 产品利率
      orderNode: 'string1111111', // 订单节点
      status: 1, // 订单状态
      failMessage: 'string', // 未通过原因
      createTime: 1758699327000, // 订创建时间
      updateTime: 1758699327000, // 订单更新时间
    },
  ];
  const permission = resolveDirective('permission');
  const copy = resolveDirective('copy');
  const emit = defineEmits(['update-success']);
  const formRef = ref<FormInst | null>(null);

  const showModal = ref(false);

  // 初始表单状态
  const initialFormState: UpdateOrderStatusParams = {
    innerOrderNo: '',
    orderNode: '',
    nodeStatus: null,
    remark: '',
    ext: {
      signUrl: undefined,
      productName: undefined,
      creditLine: undefined,
      productRate: undefined,
      term: undefined,
      loanAmount: undefined,
      monthlyPayment: undefined,
    },
  };

  const model = reactive<UpdateOrderStatusParams>({ ...initialFormState });

  const validateNumber = (fieldName: string) => {
    return (_rule, value) => {
      if (!value && value !== 0) {
        return new Error(`请输入${fieldName}`);
      }
      const num = Number(value);
      return !isNaN(num) ? true : new Error(`请输入有效数字`);
    };
  };

  const rules: FormRules = {
    orderNode: { required: true, message: '请选择订单节点', trigger: 'change' },
    nodeStatus: { required: true, type: 'number', message: '请选择订单状态', trigger: 'change' },
    remark: {
      required: true,
      message: '请输入未通过原因',
      trigger: ['blur', 'input'],
    },
    'ext.signUrl': { required: true, message: '请输入预审链接', trigger: ['blur', 'input'] },
    'ext.productName': { required: true, message: '请输入进件产品', trigger: ['blur', 'input'] },
    'ext.creditLine': {
      required: true,
      message: '请输入授信金额',
      validator: validateNumber('授信金额'),
      trigger: ['blur', 'input'],
    },
    'ext.productRate': {
      required: true,
      message: '请输入授信利率',
      validator: validateNumber('授信利率'),
      trigger: ['blur', 'input'],
    },
    'ext.term': {
      required: true,
      message: '请输入授信期数',
      validator: validateNumber('授信期数'),
      trigger: ['blur', 'input'],
    },
    'ext.loanAmount': {
      required: true,
      message: '请输入放款金额',
      validator: validateNumber('放款金额'),
      trigger: ['blur', 'input'],
    },
    'ext.monthlyPayment': {
      required: true,
      message: '请输入月供',
      validator: validateNumber('月供'),
      trigger: ['blur', 'input'],
    },
  };

  function openUpdateStatusModal(row: LoanOrderRecord) {
    // 重置表单
    Object.assign(model, JSON.parse(JSON.stringify(initialFormState)));
    // 填充表单
    model.innerOrderNo = row.innerOrderNo;
    model.orderNode = row.orderNode;
    model.nodeStatus = row.status;
    model.remark = row.failMessage;
    model.ext = {
      signUrl: row.signUrl,
      productName: row.productName,
      creditLine: row.creditLine,
      productRate: row.productRate,
      term: row.term,
      loanAmount: row.loanAmount,
      monthlyPayment: row.monthlyPayment,
    };
    showModal.value = true;
  }

  async function handleConfirm() {
    try {
      await formRef.value?.validate();

      await updateOrderStatus(model);

      window.$message.success('更新成功');
      emit('update-success');

      showModal.value = false;
    } catch (errors) {
      window.$message.error('请检查表单输入');
      return false;
    }
  }

  const createColumns = (): DataTableColumns<LoanOrderRecord> => [
    {
      title: '签约记录ID',
      key: 'innerOrderNo',
      ellipsis: {
        tooltip: true,
      },
      width: 130,
      align: 'center',
    },
    {
      title: '签约类型',
      key: 'loanOrderNo',
      ellipsis: {
        tooltip: true,
      },
      width: 130,
      align: 'center',
    },
    { title: '签约手机号', key: 'managementCode', width: 100, align: 'center' },
    {
      title: '签约地址',
      key: 'orderNode',
      width: 120,
      align: 'center',
      render(row) {
        return withDirectives(
          h(
            'p',
            {
              class: 'copy-text',
              style: {
                whiteSpace: 'nowrap',
              },
            },
            row.orderNode
          ),
          [[copy, row.orderNode]]
        );
      },
    },
    {
      title: '支付地址',
      key: 'status',
      width: 100,
      align: 'center',
      render(row) {
        return orderStatusOptions.find((opt) => opt.value === row.status)?.label || '--';
      },
    },
    { title: '签约状态', key: 'loanAmount', width: 110, align: 'center' },
    {
      title: '创建时间',
      key: 'createTime',
      width: 200,
      align: 'center',
      render(row) {
        return h(NTime, { time: new Date(row.createTime) });
      },
    },
    {
      title: '存证时间',
      key: 'updateTime',
      width: 200,
      align: 'center',
      render(row) {
        return h(NTime, { time: new Date(row.updateTime) });
      },
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 100,
      align: 'center',
      render(row) {
        return withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              onClick: () => openUpdateStatusModal(row),
            },
            { default: () => '更新状态' }
          ),
          [[permission, { action: `detail_status` }]]
        );
      },
    },
  ];

  const columns = createColumns();

  const renderCell = (value: any) => {
    if (!value) return '--';
    return value;
  };
  //TODO 1.7.1德易资方
  const showSubmitModal = ref(false);
  const fallUpFormRef = ref<FormInst | null>(null);
  // 初始表单状态
  const initialFallUpFormState: fallUpParams = {
    clueId: '',
    followCapital: '',
  };
  const fallUpModel = reactive<fallUpParams>({ ...initialFallUpFormState });
  const capitalOptions = [
    { label: '德易', value: 1 },
    { label: '易顺', value: 2 },
  ];
  const fallUpRules: FormRules = {
    followCapital: {
      required: true,
      message: '请选择进件资方',
      trigger: 'change',
    },
  };

  function openAddModal() {
    // 重置表单
    Object.assign(fallUpModel, JSON.parse(JSON.stringify(initialFallUpFormState)));
    showSubmitModal.value = true;
  }
  async function handleFallUpConfirm() {
    try {
      await fallUpFormRef.value?.validate();
      //TODO
      //await updateOrderStatus(model);
      window.$message.success('更新成功');
      emit('update-success');
      showSubmitModal.value = false;
    } catch (errors) {
      window.$message.success('提交进件失败');
      return false;
    }
  }
</script>

<style scoped>
  .w-full {
    width: 100%;
  }
</style>
<style lang="less" scoped>
  .table-actions {
    margin: 0 0 10px;
  }
</style>
