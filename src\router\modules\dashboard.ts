import { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/router/constant';
import { DashboardOutlined } from '@vicons/antd';
import { renderIcon } from '@/utils/index';

const routeName = 'dashboard';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/dashboard',
    name: routeName,
    redirect: '/dashboard/welcome',
    component: Layout,
    meta: {
      title: 'Dashboard',
      icon: renderIcon(DashboardOutlined),
      sort: 0,
    },
    children: [
      {
        path: 'workplace',
        name: `${routeName}_workplace`,
        meta: {
          title: '工作台',
          keepAlive: true,
          buttonsPreFix: 'dashboard_workplace_',
          buttons: [
            { key: 'export', name: '导出' },
            { key: 'claim', name: '认领线索' },
            { key: 'add', name: '新增客户' },
            { key: 'follow_up', name: '跟进线索' },
            { key: 'detail', name: '详情' },
            { key: 'update_info', name: '补充资料' },
            { key: 'detail_claim', name: '开始跟进' },
            { key: 'detail_add', name: '添加跟进记录' },
            { key: 'detail_mark', name: '标记状态' },
            { key: 'detail_update', name: '补充资料' },
            { key: 'detail_status', name: '更新状态' },
          ],
        },
        component: () => import('@/views/dashboard/workplace/index.vue'),
      },
      {
        path: 'welcome',
        name: `${routeName}_welcome`,
        meta: {
          title: '欢迎',
        },
        component: () => import('@/views/dashboard/welcome/index.vue'),
      },
    ],
  },
];

export default routes;
