import { createEnumOptions } from '@/utils/createEnumOptions';

// 线索来源枚举
export enum ClueSource {
  // 车贷业务
  CarLoan = 0,
  // 大联登媒体
  Media = 1,
}
export const ClueSourceMap = {
  [ClueSource.CarLoan]: '车贷业务',
  [ClueSource.Media]: '大联登媒体',
};
export const ClueSourceOptions = createEnumOptions(ClueSourceMap);

// 来源媒体枚举
export enum MediaSource {
  // 应用市场
  AppMarket = 0,
  // 信息流
  InformationFlow = 1,
  // 同业
  Industry = 2,
  // 全流程API
  AllFlowApi = 5,
}
export const MediaSourceMap = {
  [MediaSource.AppMarket]: '应用市场',
  [MediaSource.InformationFlow]: '信息流',
  [MediaSource.Industry]: '同业',
  [MediaSource.AllFlowApi]: '全流程API',
};
export const MediaSourceOptions = createEnumOptions(MediaSourceMap);

// 跟进状态枚举
export enum FollowStatus {
  // 待跟进
  WaitFollow = 0,
  // 跟进中
  Following = 1,
  // 跟进结束
  FollowEnd = 2,
}
export const FollowStatusMap = {
  [FollowStatus.WaitFollow]: '待跟进',
  [FollowStatus.Following]: '跟进中',
  [FollowStatus.FollowEnd]: '跟进结束',
};
export const FollowStatusOptions = createEnumOptions(FollowStatusMap);

// 线索状态枚举
export enum ClueStatus {
  // 待首次跟进
  WaitFirstFollow = 0,
  // 待再次跟进
  WaitSecondFollow = 1,
  // 待转化
  WaitConvert = 2,
  // 已转化
  Converted = 3,
  // 已失效
  Invalid = 4,
}
export const ClueStatusMap = {
  [ClueStatus.WaitFirstFollow]: '待首次跟进',
  [ClueStatus.WaitSecondFollow]: '待再次跟进',
  [ClueStatus.Converted]: '已转化',
  [ClueStatus.WaitConvert]: '待转化',
  [ClueStatus.Invalid]: '已失效',
};
export const ClueStatusOptions = createEnumOptions(ClueStatusMap);

// 通讯状态枚举
export enum ContactStatus {
  // 已接通
  Connected = 1,
  // 空号
  // EmptyNumber = 2,
  // 拒接
  // Reject = 3,
  // 未拨通
  Unconnected = 0,
  // 已回复
  Replied = 5,
  // 发信息未回复
  SentMessageNotReplied = 6,
  // 拉黑/删除
  Blocked = 7,
}
export const ContactStatusMap = {
  [ContactStatus.Connected]: '已接通',
  // [ContactStatus.EmptyNumber]: '空号',
  // [ContactStatus.Reject]: '拒接',
  [ContactStatus.Unconnected]: '未接通',
  [ContactStatus.Replied]: '已回复',
  [ContactStatus.SentMessageNotReplied]: '发信息未回复',
  [ContactStatus.Blocked]: '拉黑/删除',
};
export const ContactStatusOptions = createEnumOptions(ContactStatusMap);

// 意向度枚举
export enum IntentionDegree {
  // 无意向
  None = 1,
  // 低意向
  Low = 2,
  // 中意向
  Middle = 3,
  // 高意向
  High = 4,
}
export const IntentionDegreeMap = {
  [IntentionDegree.High]: '高意向',
  [IntentionDegree.Middle]: '中意向',
  [IntentionDegree.Low]: '低意向',
  [IntentionDegree.None]: '无意向',
};
export const IntentionDegreeOptions = createEnumOptions(IntentionDegreeMap);

// 是否加微枚举
export enum IsAddWeChat {
  // 否
  No = 0,
  // 是
  Yes = 1,
}
export const IsAddWeChatMap = {
  [IsAddWeChat.No]: '否',
  [IsAddWeChat.Yes]: '是',
};
export const IsAddWeChatOptions = createEnumOptions(IsAddWeChatMap);

/** 启用/禁用 */
export enum EnableDisable {
  Enable = 1,
  Disable = 0,
}
export const EnableDisableMap = {
  [EnableDisable.Enable]: '启用',
  [EnableDisable.Disable]: '禁用',
};
export const EnableDisableOptions = createEnumOptions(EnableDisableMap);

// 线索跟进方枚举
export enum ClueFollower {
  // 自有CRM
  SelfCRM = 0,
  // 易顺CRM
  EasyCRM = 1,
}
export const ClueFollowerMap = {
  [ClueFollower.SelfCRM]: '自有CRM',
  [ClueFollower.EasyCRM]: '易顺CRM',
};
export const ClueFollowerOptions = createEnumOptions(ClueFollowerMap);

// 入库方式枚举
export enum ClueStoreType {
  // API接口
  API = 0,
  // 手动增加
  Manual = 1,
}
export const ClueStoreTypeMap = {
  [ClueStoreType.API]: 'API接口',
  [ClueStoreType.Manual]: '手动增加',
};
export const ClueStoreTypeOptions = createEnumOptions(ClueStoreTypeMap);

// 通讯详情：0:未知，1：通话中，2：未接，3：拒接，4：空号,5:停机 6:呼入 7：呼出
export enum Communication {
  unknown = 0,
  onLine = 1,
  noAnswered = 2,
  rejectAnswered = 3,
  nullNumber = 4,
  closeDown = 5,
  CALL_IN = 6,
  CALL_OUT = 7,
}
export const CommunicationMap = {
  [Communication.unknown]: '未知',
  [Communication.onLine]: '通话中',
  [Communication.noAnswered]: '未接',
  [Communication.rejectAnswered]: '拒接',
  [Communication.nullNumber]: '空号',
  [Communication.closeDown]: '停机',
  [Communication.CALL_IN]: '呼入',
  [Communication.CALL_OUT]: '呼出',
};
export enum AssignType {
  //可分配
  CanAssign = 0,
  //可认领
  No_CanClaim = 1,
}
export const AssignTypeMap = {
  [AssignType.CanAssign]: '可分配',
  [AssignType.No_CanClaim]: '不可分配',
};
export const AssignTypeOptions = createEnumOptions(AssignTypeMap);
/**转换方式0：放款；1：分发 */
export enum ConvertType {
  Loan = 0,
  Distribute = 1,
}
export const ConvertTypeMap = {
  [ConvertType.Loan]: '放款',
  [ConvertType.Distribute]: '分发',
};
export const ConvertTypeOptions = createEnumOptions(ConvertTypeMap);
/**转化状态0：未转化  1：已转化； */
export enum ConvertStatus {
  No_Convert = 0,
  Convert = 1,
}
export const ConvertStatusMap = {
  [ConvertStatus.No_Convert]: '未转化',
  [ConvertStatus.Convert]: '已转化',
};
export const ConvertStatusOptions = createEnumOptions(ConvertStatusMap);

/** 线索类型 有效线索、无效线索 */
export enum ClueType {
  Valid = 1,
  Invalid = 0,
}
export const ClueTypeMap = {
  [ClueType.Valid]: '有效线索',
  [ClueType.Invalid]: '无效线索',
};
export const ClueTypeOptions = createEnumOptions(ClueTypeMap);

/** 线索出价类型 **/
export enum ClueBidType {
  One = 1,
  Two = 2,
  Three = 3,
  Four = 4,
}
export const ClueBidTypeMap = {
  [ClueBidType.One]: '一类线索',
  [ClueBidType.Two]: '二类线索',
  [ClueBidType.Three]: '三类线索',
  [ClueBidType.Four]: '四类线索',
};
export const ClueBidTypeOptions = createEnumOptions(ClueBidTypeMap);

/** 无效类型 无意愿、明确拒绝、资质不符、系统变更 */
export enum InvalidType {
  No_Willing = 0,
  Explicit_Rejection = 1,
  Qualification_Mismatch = 2,
  System_Change = 3,
}
export const InvalidTypeMap = {
  [InvalidType.No_Willing]: '无意愿',
  [InvalidType.Explicit_Rejection]: '明确拒绝',
  [InvalidType.Qualification_Mismatch]: '资质不符',
  [InvalidType.System_Change]: '系统变更',
};
export const InvalidTypeOptions = createEnumOptions(InvalidTypeMap);
