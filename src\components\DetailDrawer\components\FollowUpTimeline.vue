<template>
  <div class="timeline-container" ref="timelineRef">
    <n-timeline>
      <n-timeline-item
        v-for="item in records"
        :key="item.id"
        :type="getTagType(item.followUpType, item.communicationStatus)"
      >
        <template #header>
          <div class="timeline-header">
            <span>{{ item.createByName }}</span>
            <span class="timestamp">
              <n-time :time="new Date(item.createTime)" />
            </span>
            <n-tag size="small" :type="getTagType(item.followUpType, item.communicationStatus)">
              {{ getStatusLabel(item.followUpType, item.communicationStatus) }}
            </n-tag>
          </div>
        </template>
        <div class="timeline-content">
          <div
            v-if="
              (item.communicationType || item.communicationType === 0) &&
              CallTypeValue[item.communicationType]
            "
            >通话类型：{{ CallTypeValue[item.communicationType] }}</div
          >
          <div
            v-if="
              (item.outCallType || item.outCallType === 0) && ExhalationMethod[item.outCallType]
            "
            >呼出方式：{{ ExhalationMethod[item.outCallType] }}</div
          >
          <div v-if="item.communicationDetail || item.communicationDetail === 0"
            >通讯详情：{{ getDetailLabe(item.communicationDetail) }}</div
          >
          <div v-if="item.communicationResult">通讯失败原因：{{ item.communicationResult }}</div>
          <div class="flex items-center" v-if="item.communicationRecording"
            >通话录音：{{ item.duration ? formatTime(item.duration) : '00:00' }}
            <n-button
              @click="(showModal = true), (voiceSrc = item.communicationRecording)"
              text
              style="font-size: 24px; margin-left: 5px"
            >
              <template #icon>
                <n-icon><PlayCircle /></n-icon>
              </template>
            </n-button>
          </div>
          <div class="no-community-result flex" v-if="noCommunityDetail(item)">
            <div class="left-babel w-80px flex-shrink-0">
              <p>通话类型：</p>
              <p>呼出方式：</p>
              <p>通讯详情：</p>
              <p>通话录音：</p>
            </div>
            <div class="right-status flex-grow-1 ml-5px flex justify-center items-center">
              <n-icon>
                <SearchSharp />
              </n-icon>
              <span>通讯状态查询中...</span>
            </div>
          </div>
          <div class="flex items-center"
            >备注:
            <template v-if="item.remark">{{ item.remark }}</template>
            <n-button
              text
              v-else
              style="font-size: 24px; margin-left: 5px"
              @click="
                (showModal1 = true), (markForm = { remark: '', id: item.id, clueId: item.clueId })
              "
            >
              <template #icon>
                <n-icon>
                  <AddCircle />
                </n-icon>
              </template>
            </n-button>
          </div>
        </div>
      </n-timeline-item>
    </n-timeline>
    <BaseModal v-model:show="showModal" :width="600" title="通话记录">
      <VoiceControl :src="voiceSrc" :enablePlaybackPosition="false" />
    </BaseModal>
    <BaseModal
      v-model:show="showModal1"
      :width="600"
      title="添加跟进备注"
      :onConfirm="submitMarkForm"
    >
      <AddMark ref="markFormRef" v-model:form-model="markForm" />
    </BaseModal>
  </div>
</template>

<script lang="ts" setup>
  import emitter from '@/utils/eventBus';
  import { useTemplateRef, onUnmounted, ref, computed } from 'vue';
  import { NTimeline, NTimelineItem, NTag, NTime } from 'naive-ui';
  import type { ClueFollowUpRecord } from '@/api/detail';
  import { updateFollowUpRecord } from '@/api/detail';
  import { PlayCircle, AddCircle, SearchSharp } from '@vicons/ionicons5';
  import { BaseModal } from '@/components/Modal';
  import VoiceControl from '@/components/VoiceControl/index.vue';
  import AddMark from './AddMark.vue';
  import {
    followUpMethodEnum,
    phoneCommunicationStatusOptions,
    wechatCommunicationStatusOptions,
    phoneCommunicationDetailOptions,
    communicationStatusEnum,
    CallTypeValue,
    ExhalationMethod,
  } from '@/enums/detailEnum';
  import { formatTime } from '@/utils/dateUtil';
  const props = defineProps<{
    records: ClueFollowUpRecord[];
  }>();
  interface formModelType {
    remark: string;
    id?: number | string;
    clueId?: number | string;
  }
  const showModal = ref(false);
  const showModal1 = ref(false);
  const voiceSrc = ref();
  const markForm = ref<formModelType>({ remark: '', id: '', clueId: '' });
  const timelineRef = useTemplateRef<HTMLDivElement>('timelineRef');
  const markFormRef = ref();
  function getTagType(_type: followUpMethodEnum, status: number) {
    const successStatus = [communicationStatusEnum.CONNECTED, communicationStatusEnum.REPLIED];
    const errorStatus = [
      communicationStatusEnum.BLACKLISTED,
      communicationStatusEnum.NOT_CONNECTED,
    ];

    if (successStatus.includes(status)) {
      return 'success';
    }
    if (errorStatus.includes(status)) {
      return 'error';
    }
    return 'info';
  }

  function getStatusLabel(type: followUpMethodEnum, status: number) {
    const methodLabel = type === followUpMethodEnum.PHONE ? '电话沟通' : '微信沟通';

    const options =
      type === followUpMethodEnum.PHONE
        ? phoneCommunicationStatusOptions
        : wechatCommunicationStatusOptions;

    const statusLabel = options.find((opt) => opt.value === status)?.label || '';

    return `${methodLabel}${statusLabel ? '-' + statusLabel : ''}`;
  }
  function getDetailLabe(value: number) {
    return phoneCommunicationDetailOptions.find((item) => item.value === value)?.label ?? '-';
  }
  function scrollToTop() {
    timelineRef.value?.scrollTo(0, 0);
  }

  emitter.on('add-follow-up-success', scrollToTop);

  onUnmounted(() => {
    emitter.off('add-follow-up-success', scrollToTop);
  });
  async function submitMarkForm() {
    await markFormRef.value?.validateForm();
    let res = await updateFollowUpRecord(markForm.value);
    //刷新详情
    if (res.code == 200) {
      emitter.emit('reloadDetailData');
      return Promise.resolve();
    }
    throw new Error('添加失败');
  }
  //处理判断通话详情是否无回调
  function noCommunityDetail(item) {
    let noCall = true;
    // const filterKey = [
    //   'communicationDetail',
    //   'communicationRecording',
    //   'communicationType',
    //   'outCallType',
    //   'communicationResult',
    // ];
    // if (item.followUpType === 1) {
    //   for (let i = 0; i < filterKey.length; i++) {
    //     const key = filterKey[i];
    //     if (item[key] !== null && item[key] !== undefined && item[key] !== '') {
    //       noCall = false;
    //       break;
    //     }
    //   }
    // } else {
    //   noCall = false;
    // }
    if (item.followUpType === 1) {
      const callStatus = phoneCommunicationStatusOptions.map((item) => item.value);
      if (callStatus.includes(item.communicationStatus)) noCall = false;
    } else {
      noCall = false;
    }
    return noCall;
  }
</script>

<style lang="less" scoped>
  @import '@/styles/custom/scrollbar.less';

  .timeline-container {
    border: 1px solid #eee;
    max-height: 380px;
    overflow-y: auto;
    padding: 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    .custom-scrollbar();
  }
  .timeline-header {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  .timestamp {
    color: #999;
  }
  .timeline-content {
    color: #666;
    margin-top: 8px;
    font-size: 13px;
    line-height: 1.6;
  }
  .right-status {
    padding: 0 20px;
    border: 1px dotted #333;
  }
</style>
