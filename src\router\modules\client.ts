import { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/router/constant';
import { OptionsSharp } from '@vicons/ionicons5';
import { renderIcon } from '@/utils';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/client',
    name: 'Client',
    redirect: '/client/high-seas',
    component: Layout,
    meta: {
      title: '客户管理',
      icon: renderIcon(OptionsSharp),
      sort: 3,
    },
    children: [
      {
        path: 'high-seas',
        name: 'HighSeas',
        meta: {
          title: '客户公海',
          buttonsPreFix: 'client_high_seas_',
          buttons: [
            { key: 'export', name: '导出' },
            { key: 'add', name: '新增线索' },
            { key: 'detail', name: '详情' },
            { key: 'detail_add', name: '添加跟进记录' },
            { key: 'detail_mark', name: '标记状态' },
            { key: 'detail_update', name: '补充资料' },
            { key: 'detail_status', name: '更新状态' },
          ],
          keepAlive: true,
        },
        component: () => import('@/views/client/highSeas/index.vue'),
      },
      {
        path: 'my-clients',
        name: 'MyClients',
        meta: {
          title: '我的客户',
          buttonsPreFix: 'client_my-clients_',
          buttons: [
            { key: 'export', name: '导出' },
            { key: 'claim', name: '认领线索' },
            { key: 'add', name: '新增线索' },
            { key: 'follow_up', name: '跟进' },
            { key: 'detail', name: '详情' },
            { key: 'detail_add', name: '添加跟进记录' },
            { key: 'detail_mark', name: '标记状态' },
            { key: 'detail_update', name: '补充资料' },
            { key: 'detail_status', name: '更新状态' },
          ],
          keepAlive: true,
        },
        component: () => import('@/views/client/myClients/index.vue'),
      },
    ],
  },
];

export default routes;
