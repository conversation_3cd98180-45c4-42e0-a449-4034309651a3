import { createEnumOptions } from '@/utils/createEnumOptions';

/**
 * 线索状态
 */
export enum clueStatusEnum {
  UNCONVERTED = 1, // 未转化
  CONVERTED = 2, // 已转化
}

export const clueStatusOptions = [
  { label: '未转化', value: clueStatusEnum.UNCONVERTED },
  { label: '已转化', value: clueStatusEnum.CONVERTED },
];

/**
 * 线索类型
 */
export enum clueTypeEnum {
  INVALID = 0, // 无效
}

export const clueTypeOptions = [
  {
    value: clueTypeEnum.INVALID,
    label: '无效',
    tip: '此线索无效，不再跟进，无效线索不可变更为有效',
  },
];

/**
 * 跟进状态
 */
export enum followUpStatusEnum {
  AWAITING = 0, // 待跟进
  FOLLOWING = 1, // 跟进中
  COMPLETED = 2, // 跟进结束
}

export const followUpStatusOptions = [
  { label: '待跟进', value: followUpStatusEnum.AWAITING },
  { label: '跟进中', value: followUpStatusEnum.FOLLOWING },
  { label: '跟进结束', value: followUpStatusEnum.COMPLETED },
];

/**
 * 跟进节点状态
 */
export enum FollowUpNodeStatusEnum {
  AWAITING_FIRST = 0, // 待首次跟进
  AWAITING_AGAIN = 1, // 待再次跟进
  AWAITING_CONVERSION = 2, // 待转化
  CONVERTED = 3, // 已转化
  INVALID = 4, // 已失效
}

export const followUpNodeStatusOptions = [
  { label: '待首次跟进', value: FollowUpNodeStatusEnum.AWAITING_FIRST },
  { label: '待再次跟进', value: FollowUpNodeStatusEnum.AWAITING_AGAIN },
  { label: '待转化', value: FollowUpNodeStatusEnum.AWAITING_CONVERSION },
  { label: '已转化', value: FollowUpNodeStatusEnum.CONVERTED },
  { label: '已失效', value: FollowUpNodeStatusEnum.INVALID },
];

/**
 * 意向度
 */
export enum intentionEnum {
  NONE = 1, // 无意向
  LOW = 2, // 低意向
  MEDIUM = 3, // 中意向
  HIGH = 4, // 高意向
}

export const intentionOptions = [
  {
    value: intentionEnum.NONE,
    label: '无意向',
    shortLabel: '无',
    tip: '客户明确表示无需求或已选择其他产品',
  },
  {
    value: intentionEnum.LOW,
    label: '低意向',
    shortLabel: '低',
    tip: '客户有初步了解，但近期无明确计划',
  },
  {
    value: intentionEnum.MEDIUM,
    label: '中意向',
    shortLabel: '中',
    tip: '客户有明确需求，正在比较或考虑中',
  },
  {
    value: intentionEnum.HIGH,
    label: '高意向',
    shortLabel: '高',
    tip: '客户有强烈的购买意愿，近期可能成交',
  },
];

/**
 * 订单状态
 */
export enum orderStatusEnum {
  PASS = 1, // 通过
  REJECT = 3, // 未通过
}

export const orderStatusOptions = [
  { label: '通过', value: orderStatusEnum.PASS },
  { label: '未通过', value: orderStatusEnum.REJECT },
];

/**
 * 订单节点
 */
export enum orderNodeEnum {
  PRE_AUDIT = '003', // 预审生成
  PRE_AUDIT_APPROVAL = '010', // 预审审批
  CUSTOMER_CALL = '110', // 客服电核
  CREDIT = '210', // 授信
  LOAN = '310', // 放款
}

export const orderNodeOptions = [
  { label: '预审生成', value: orderNodeEnum.PRE_AUDIT },
  { label: '预审审批', value: orderNodeEnum.PRE_AUDIT_APPROVAL },
  { label: '客服电核', value: orderNodeEnum.CUSTOMER_CALL },
  { label: '授信', value: orderNodeEnum.CREDIT },
  { label: '放款', value: orderNodeEnum.LOAN },
];

/**
 * 跟进方式
 */
export enum followUpMethodEnum {
  PHONE = 1, // 电话外呼
  WECHAT = 2, // 微信沟通
}

export const followUpMethodOptions = [
  { label: '电话外呼', value: followUpMethodEnum.PHONE },
  { label: '微信沟通', value: followUpMethodEnum.WECHAT },
];

/**
 * 通讯状态
 */
export enum communicationStatusEnum {
  CONNECTED = 1, // 已接通
  // EMPTY_NUMBER = 2, // 空号
  // REJECTED = 3, // 拒接
  NOT_CONNECTED = 0, // 未拨通
  REPLIED = 5, // 已回复
  MESSAGE_NOT_REPLIED = 6, // 发消息未回复
  BLACKLISTED = 7, // 拉黑/删除
}
//通讯详情：0:未知，1：通话中，2：未接，3：拒接，4：空号 5:停机 6:呼入 7：呼出
export enum communicationDetailEnum {
  UNKNOWN = 0,
  ONLINE = 1,
  NOT_CONNECTED = 2,
  REJECT_CONNECTED = 3,
  NULL = 4,
  CLOSE_DOWN = 5,
  CALL_IN = 6,
  CALL_OUT = 7,
}
/**
 * 通讯状态2
 */
export enum communicationStatusEnumV2 {
  CONNECTED = 1, // 已接通
  NOT_CONNECTED = 0, // 未拨通
}
export const phoneCommunicationStatusOptions = [
  { label: '已接通', value: communicationStatusEnumV2.CONNECTED },
  { label: '未拨通', value: communicationStatusEnumV2.NOT_CONNECTED },
];

export const wechatCommunicationStatusOptions = [
  { label: '已回复', value: communicationStatusEnum.REPLIED },
  { label: '发消息未回复', value: communicationStatusEnum.MESSAGE_NOT_REPLIED },
  { label: '拉黑/删除', value: communicationStatusEnum.BLACKLISTED },
];

export const phoneCommunicationDetailOptions = [
  { label: '未知', value: communicationDetailEnum.UNKNOWN },
  { label: '通话中', value: communicationDetailEnum.ONLINE },
  { label: '未接', value: communicationDetailEnum.NOT_CONNECTED },
  { label: '拒接', value: communicationDetailEnum.REJECT_CONNECTED },
  { label: '空号', value: communicationDetailEnum.NULL },
  { label: '停机', value: communicationDetailEnum.CLOSE_DOWN },
  { label: '呼入', value: communicationDetailEnum.CALL_IN },
  { label: '呼出', value: communicationDetailEnum.CALL_OUT },
];
export const phoneConnectedOptions = [
  { label: '未知', value: communicationDetailEnum.UNKNOWN },
  { label: '未接', value: communicationDetailEnum.NOT_CONNECTED },
  { label: '拒接', value: communicationDetailEnum.REJECT_CONNECTED },
];
export const phoneNoConnectedOptions = [
  { label: '空号', value: communicationDetailEnum.NULL },
  { label: '通话中', value: communicationDetailEnum.ONLINE },
  { label: '未知', value: communicationDetailEnum.UNKNOWN },
  { label: '停机', value: communicationDetailEnum.CLOSE_DOWN },
];
/**
 * 是否枚举（通用）
 */
export enum YesNoEnum {
  NO = 0,
  YES = 1,
}

export const yesNoOptions = [
  { label: '是', value: YesNoEnum.YES },
  { label: '否', value: YesNoEnum.NO },
];

/**
 * 车辆状态枚举
 */
export enum VehicleStatusEnum {
  FULL_PAYMENT = 1, // 全款
  LOAN_SETTLED = 2, // 贷款-已结清
  LOAN_UNSETTLED = 3, // 贷款-未结清
}

export const vehicleStatusOptions = [
  { label: '全款', value: VehicleStatusEnum.FULL_PAYMENT },
  { label: '贷款-已结清', value: VehicleStatusEnum.LOAN_SETTLED },
  { label: '贷款-未结清', value: VehicleStatusEnum.LOAN_UNSETTLED },
];

/**
 * 校验状态枚举
 */
export enum CheckStatusEnum {
  FAILED = 0, // 未通过
  PASSED = 1, // 通过
}

export const checkStatusOptions = [
  { label: '未通过', value: CheckStatusEnum.FAILED },
  { label: '通过', value: CheckStatusEnum.PASSED },
];

/**
 * 待办类型
 */
export enum todoTypeEnum {
  CONTACT_REMIND = 1, // 联系提醒
}

export const todoTypeOptions = [{ label: '联系提醒', value: todoTypeEnum.CONTACT_REMIND }];

/**
 * 性别
 */
export enum sexEnum {
  MALE = 1, // 男
  FEMALE = 0, // 女
}

export const sexOptions = [
  { label: '男', value: sexEnum.MALE },
  { label: '女', value: sexEnum.FEMALE },
];

/**
 * 学历
 */
export enum educationLevelEnum {
  SENIOR_HIGH = 1, // 高中
  JUNIOR_COLLEGE = 2, // 大专
  BACHELOR = 3, // 本科
  MASTER = 4, // 研究生
  DOCTOR = 5, // 博士
}

export const educationLevelOptions = [
  { label: '高中', value: educationLevelEnum.SENIOR_HIGH },
  { label: '大专', value: educationLevelEnum.JUNIOR_COLLEGE },
  { label: '本科', value: educationLevelEnum.BACHELOR },
  { label: '研究生', value: educationLevelEnum.MASTER },
  { label: '博士', value: educationLevelEnum.DOCTOR },
];

/**
 * 婚姻状况
 */
export enum maritalStatusEnum {
  SINGLE = 1, // 未婚
  MARRIED = 2, // 已婚
}

export const maritalStatusOptions = [
  { label: '未婚', value: maritalStatusEnum.SINGLE },
  { label: '已婚', value: maritalStatusEnum.MARRIED },
];

/**
 * 房产类型
 */
export enum houseTypeEnum {
  COMMODITY = 1, // 商品房
  SELF_BUILT = 2, // 自建房
  VILLA = 3, // 别墅
  OFFICE_BUILDING = 4, // 写字楼
  APARTMENT = 5,
}

export const houseTypeOptions = [
  { label: '商品房', value: houseTypeEnum.COMMODITY },
  { label: '自建房', value: houseTypeEnum.SELF_BUILT },
  { label: '别墅', value: houseTypeEnum.VILLA },
  { label: '写字楼', value: houseTypeEnum.OFFICE_BUILDING },
  { label: '公寓', value: houseTypeEnum.APARTMENT },
];

/**
 * 单位性质
 */
export enum companyNatureEnum {
  INSTITUTION = 1, // 企事业单位
  PRIVATE_ENTERPRISE = 2, // 民营企业
  INDIVIDUAL_BUSINESS = 3, // 工商个体户
  FOREIGN_COMPANY = 4, // 外资公司
  OTHER = 5, // 其他
}

export const companyNatureOptions = [
  { label: '企事业单位', value: companyNatureEnum.INSTITUTION },
  { label: '民营企业', value: companyNatureEnum.PRIVATE_ENTERPRISE },
  { label: '工商个体户', value: companyNatureEnum.INDIVIDUAL_BUSINESS },
  { label: '外资公司', value: companyNatureEnum.FOREIGN_COMPANY },
  { label: '其他', value: companyNatureEnum.OTHER },
];

/**
 * 月收入
 */
export enum monthlyIncomeEnum {
  LT_5000 = 1, // 5000以下
  '5000_10000' = 2, // 5000-10000
  '10000_15000' = 3, // 10000-15000
  '15000_20000' = 4, // 15000-20000
  GT_20000 = 5, // 20000以上
}

export const monthlyIncomeOptions = [
  { label: '5000', value: monthlyIncomeEnum.LT_5000 },
  { label: '5000-10000', value: monthlyIncomeEnum['5000_10000'] },
  { label: '10000-15000', value: monthlyIncomeEnum['10000_15000'] },
  { label: '15000-20000', value: monthlyIncomeEnum['15000_20000'] },
  { label: '20000以上', value: monthlyIncomeEnum.GT_20000 },
];

/**
 * 联系人关系
 */
export enum contactRelationEnum {
  PARENT = 1, // 父/母
  SPOUSE = 2, // 配偶
}

export const contactRelationOptions = [
  { label: '父/母', value: contactRelationEnum.PARENT },
  { label: '配偶', value: contactRelationEnum.SPOUSE },
];
//通话类型
export enum CallTypeValueEnum {
  CALLOUT = 0,
  CALLIN = 1,
}
export const CallTypeValue = {
  [CallTypeValueEnum.CALLIN]: '呼入',
  [CallTypeValueEnum.CALLOUT]: '呼出',
};

//呼出方式 1，业务系统发起；2，手机发起
export enum ExhalationMethodEnum {
  SYSTEM = 1,
  PHONE = 2,
}

export const ExhalationMethod = {
  [ExhalationMethodEnum.SYSTEM]: '系统呼出',
  [ExhalationMethodEnum.PHONE]: '手机发起',
};

// 车辆估值 0：0-5万 1：5-10万 2：10-20万 3：20万以上
export enum VehicleValueEnum {
  ZERO_TO_FIVE = 0,
  FIVE_TO_TEN = 1,
  TEN_TO_TWENTY = 2,
  OVER_TWENTY = 3,
}

export const CehicleValue = {
  [VehicleValueEnum.ZERO_TO_FIVE]: '0-5万',
  [VehicleValueEnum.FIVE_TO_TEN]: '5-10万',
  [VehicleValueEnum.TEN_TO_TWENTY]: '10-20万',
  [VehicleValueEnum.OVER_TWENTY]: '20万以上',
};

export const vehicleValueOptions = createEnumOptions(CehicleValue);
