<template>
  <div class="follow-up-details">
    <FollowUpStatusStepper :current-status="detail.clueInfoVo.followStatusIndex" />

    <div v-if="detail.clueInfoVo.followStatusIndex === FollowUpNodeStatusEnum.AWAITING_FIRST">
      <AwaitingFirstFollowUp
        :create-time="detail.clueInfoVo.triageTime"
        @start-follow-up="onStart"
      />
    </div>
    <div v-else>
      <FollowUpTimeline :records="detail.clueFollowUpRecords" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import type { CustomerDetail } from '@/api/detail';
  import { FollowUpNodeStatusEnum } from '@/enums/detailEnum';
  import FollowUpStatusStepper from './FollowUpStatusStepper.vue';
  import AwaitingFirstFollowUp from './AwaitingFirstFollowUp.vue';
  import FollowUpTimeline from './FollowUpTimeline.vue';

  defineProps<{
    detail: CustomerDetail;
  }>();

  const emit = defineEmits(['start-follow-up']);

  function onStart() {
    emit('start-follow-up');
  }
</script>
